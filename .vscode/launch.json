{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "MQTT",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/pubsub2/main.py",
            "console": "integratedTerminal",
            "preLaunchTask": "SOURCE_CYCLONE"
        },
        {
            "name": "Benchmark",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/pubsub2/benchmark.py",
            "console": "integratedTerminal"
        },
        {
            "name": "Synthetic",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/pubsub2/synthetic.py",
            "console": "integratedTerminal"
        }
    ]
}