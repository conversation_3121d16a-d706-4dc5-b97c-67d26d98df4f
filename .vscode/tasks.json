{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "SOURCE_CYCLONE", // A unique label for your task
            "type": "shell",             // The type of task (e.g., "shell", "process")
            "command": "source /opt/ros/humble/setup.bash && source /workspaces/isaac_ros-dev/src/isaac_ros_common/pubsub2/settings.rc", // The command to execute
            "presentation": {
                "reveal": "always",      // Always show the terminal output for this task
            },
            
        },
        {
            "label": "SOURCE_DEFAULT", // A unique label for your task
            "type": "shell",             // The type of task (e.g., "shell", "process")
            "command": "source /opt/ros/humble/setup.bash", // The command to execute
            "presentation": {
                "reveal": "always",      // Always show the terminal output for this task
            },
            
        }
    ]
}