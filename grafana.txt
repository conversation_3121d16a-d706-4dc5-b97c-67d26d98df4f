yesoreyeram-infinity-datasource
frser-sqlite-datasource

docker run -d -p 3000:3000 --name=grafana   -e "GF_PLUGINS_PREINSTALL=frser-sqlite-datasource, grafana-simple-json-datasource, yesoreyeram-infinity-datasource" \
  grafana/grafana-enterprise


  docker run -d -p 3000:3000 --name=grafana grafana/grafana-enterprise


docker run -d -p 3000:3000 --name grafana --network host -e "GF_PLUGINS_PREINSTALL=frser-sqlite-datasource, grafana-simple-json-datasource, yesoreyeram-infinity-datasource" -v grafana-storage:/var/lib/grafana  grafana/grafana-oss:latest

event: metric
data: {"drone_id": 1, "timestamp": 190000}


C:\Users\<USER>\Desktop\debug_archives


docker run -d -p 3000:3000 --name grafana --network host -e "GF_PLUGINS_PREINSTALL=frser-sqlite-datasource, grafana-simple-json-datasource, yesoreyeram-infinity-datasource" -v grafana-storage:/var/lib/grafana -v  C:\Users\<USER>\Desktop\debug_archives:/mnt/windows grafana/grafana-oss:latest