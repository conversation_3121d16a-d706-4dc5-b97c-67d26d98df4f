Before playing the bag or debugging source the same DDS by:

source /opt/ros/humble/setup.bash && source /workspaces/isaac_ros-dev/src/isaac_ros_common/pubsub2/settings.rc

Play a rosbag like so:

cd src/isaac_ros_common/the_bag/
ros2 bag play rosbag2_2025_04_30-09_36_26_0.db3 --loop

NOTE: The root-CA.crt and drone keys and certificates are generated at the time of drone provisioning


other useful commands:

rosdep install -i --from-path src/isaac_ros_common/pubsub2 --rosdistro humble -y
colcon build --packages-select pubsub2