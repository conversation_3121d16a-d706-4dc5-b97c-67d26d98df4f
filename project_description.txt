Real-time Metrics Dashboard - Project Description

1. Core Features

1.1 Real-time Data Visualization
- Real-time metric updates using Server-Sent Events (SSE)
- Interactive line chart using Chart.js with date-fns adapter
- Support for multiple concurrent metrics
- Automatic color assignment for different metrics
- Dynamic color generation for metrics beyond predefined colors
- Color similarity detection to ensure distinct visualization
- Dynamic x-axis display:
  * For time spans <= 30 seconds: show each second (HH:mm:ss)
  * For time spans <= 5 minutes (1m, 3m, 5m): show every 5 seconds (HH:mm:ss)
  * For time spans > 5 minutes (10m, 15m, 30m): show each minute (HH:mm)
- Dark mode support with theme-aware cursor and selection colors

1.2 Time Window Management
- Configurable time windows: 30s, 1m, 3m, 5m, 10m, 15m, 30m
- Time window scrolling with slider control (200-300px width)
- Follow/Pause functionality for real-time updates
- Cursor timestamp display with monospace font
- Automatic time window adjustment
- Heartbeat mechanism to maintain SSE connection

1.3 Metric Management
- Dynamic metric list generation
- Enable/Disable individual metrics
- Bulk enable/disable all metrics
- Metric statistics panel
- Real-time metric updates
- Persistent metric state
- SQLite-based metric storage and querying

1.4 Chart Interaction
- Region selection for detailed analysis
- Statistical analysis of selected regions
- Interactive tooltips
- Responsive chart sizing
- Dark mode support
- Cursor tracking
- Selection rectangle with semi-transparent overlay

2. Technical Specifications

2.1 Backend (Python/FastAPI)
- FastAPI web server with optimized settings
- SSE endpoint for real-time updates
- Static file serving
- Template rendering with Jinja2
- SQLite database integration
- Configurable update intervals
- Modular application structure
- CORS middleware with optimized settings
- Background task support
- Archive management system
- Graph generation capabilities
- Database backup functionality

2.2 Frontend (JavaScript/HTML/CSS)
- Modular JavaScript architecture
- Responsive Bootstrap-based UI
- Chart.js integration with date-fns adapter
- Real-time data processing
- Event-driven architecture
- Color management system
- Bootstrap Icons integration
- Responsive grid system
- Flexbox-based layout
- Media query support for different screen sizes

2.3 Data Management
- SQLite database storage
- Configurable data point limits
- Automatic data pruning
- Time-based data organization
- Metric value tracking
- Archive system for historical data
- Parquet file support for data export

3. UI Layout and Components

3.1 Main Layout
- Full-height responsive layout
- Two-column design (9:3 ratio)
- Left column: Main chart area
- Right column: Metrics control panel
- Bootstrap-based responsive grid system
- Navbar integration

3.2 Main Chart Area (Left Column)
- Full-height card container
- Top control bar with:
  * Cursor timestamp display (right-aligned, monospace font)
  * Time scroll slider (200-300px width)
  * Follow/Pause button group
  * Time window selection buttons
- Chart canvas (flexible height)
- Interactive overlay elements:
  * Selection rectangle with semi-transparent overlay
  * Vertical cursor line
  * Tooltips

3.3 Metrics Panel (Right Column)
- Two stacked cards:
  * Top card: Available Metrics
    - Header with title and bulk action buttons
    - Scrollable metric list
    - Individual metric toggles
  * Bottom card: Metric Selection
    - Header with title
    - Statistics display area
    - Selection analysis table

3.4 Control Elements
- Time Window Controls:
  * Button group with 7 preset periods
  * Follow/Pause toggle buttons
  * Time scroll slider with label
- Metric Controls:
  * Enable/Disable all buttons
  * Individual metric toggles
  * Metric list scroll area
- Chart Controls:
  * Interactive selection
  * Cursor tracking
  * Tooltip display

4. Performance Considerations

4.1 Data Management
- Efficient color management
- Optimized event handling
- SQLite query optimization
- Background task processing
- SSE connection management
- Heartbeat mechanism

4.2 UI Performance
- Responsive layout
- Efficient DOM updates
- Optimized chart rendering
- Real-time updates
- CSS optimization
- Media query optimization
- Flexbox-based layout
- Bootstrap grid system 