{% extends "base.html" %}

{% block title %}Archive - Real-time Metrics{% endblock %}

{% block content %}
<div class="container mt-3">
    <h2>Archived Metric Files</h2>
    <div class="list-group">
        {% for file in files %}
        <div class="list-group-item d-flex justify-content-between align-items-center">
            <span>{{ file }}</span>
            <a href="{{ url_for('download_archive', filename=file) }}" class="btn btn-primary btn-sm">
                <i class="bi bi-download"></i> Download
            </a>
        </div>
        {% else %}
        <p>No archived files found.</p>
        {% endfor %}
    </div>
</div>
{% endblock %} 