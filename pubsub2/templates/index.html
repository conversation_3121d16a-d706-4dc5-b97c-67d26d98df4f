{% extends "base.html" %}

{% block title %}Home - Real-time Metrics{% endblock %}

{% block content %}
<div class="container-fluid px-2 h-100">
    <div class="row h-100 g-2">
        <div class="col-md-9">
            <div class="card h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex flex-wrap justify-content-end align-items-center gap-2 mb-2">
                        <div id="cursorTimestamp" class="text-muted" style="display: none; min-width: 120px; font-size: 0.9rem;"></div>
                        <div class="d-flex align-items-center gap-2" style="min-width: 200px; max-width: 300px;">
                            <label for="timeScroll" class="mb-0">Scroll:</label>
                            <input type="range" class="form-range flex-grow-1" id="timeScroll" min="0" max="100" value="50">
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success" id="followButton">
                                <i class="bi bi-play-fill"></i> Follow
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="pauseButton">
                                <i class="bi bi-pause-fill"></i> Pause
                            </button>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" data-period="30">30s</button>
                            <button type="button" class="btn btn-outline-primary" data-period="60">1m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="180">3m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="300">5m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="600">10m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="900">15m</button>
                            <button type="button" class="btn btn-outline-primary" data-period="1800">30m</button>
                        </div>
                    </div>
                    <div class="flex-grow-1 position-relative">
                        <canvas id="metricChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3" id="metricsPanel">
            <div class="d-flex flex-column h-100 gap-2">
                <div class="card flex-grow-1 d-flex flex-column">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Available Metrics</h5>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" id="enableAllMetrics">
                                    <i class="bi bi-check-all"></i> Enable All
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="disableAllMetrics">
                                    <i class="bi bi-x-lg"></i> Disable All
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body overflow-auto flex-grow-1">
                        <div class="list-group" id="metricList">
                            <!-- Metrics will be added here dynamically -->
                        </div>
                    </div>
                </div>
                <div class="card flex-grow-1 d-flex flex-column">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Metric Selection</h5>
                    </div>
                    <div class="card-body overflow-auto flex-grow-1">
                        <div id="selectionStats">
                            <p class="text-muted">Select a region on the chart to view statistics</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Streaming Panel -->
    <div class="row mt-2">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">System Logs</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">Clear</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToBottom()">Scroll to Bottom</button>
                        <button class="btn btn-sm btn-outline-primary" onclick="scrollToTop()">Jump to Top</button>
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#logPanel" aria-expanded="false">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="collapse" id="logPanel">
                    <div class="card-body p-0">
                        <div class="table-responsive" style="max-height: 80vh; overflow-y: auto;">
                            <table class="table table-sm log-table mb-0">
                                <thead class="sticky-top bg-body">
                                    <tr>
                                        <th style="width: 15%">Timestamp</th>
                                        <th style="width: 10%">Level</th>
                                        <th style="width: 15%">Thread</th>
                                        <th style="width: 60%">Message</th>
                                    </tr>
                                </thead>
                                <tbody id="logTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="{{ url_for('static', path='css/metrics.css') }}">
<link rel="stylesheet" href="{{ url_for('static', path='css/bootstrap-icons.css') }}">
<script src="{{ url_for('static', path='js/chart.umd.js') }}"></script>
<script src="{{ url_for('static', path='js/chartjs-adapter-date-fns.bundle.min.js') }}"></script>

<script>
// Set up SSE connection
const eventSource = new EventSource('/events');

// Chart configuration
const ctx = document.getElementById('metricChart').getContext('2d');
let maxDataPoints = 30; // Default number of points to show on the chart
let allData = {}; // Store all data points for each metric
let activeMetrics = new Set(); // Track active metrics
let receivedMetrics = new Set(); // Track all metrics that have been received
let spanGapsValue = 3000;


// Available colors for metrics
const metricColors = [
    'rgb(75, 192, 192)',   // Teal
    'rgb(255, 99, 132)',   // Pink
    'rgb(54, 162, 235)',   // Blue
    'rgb(255, 159, 64)',   // Orange
    'rgb(153, 102, 255)',  // Purple
    'rgb(255, 205, 86)',   // Yellow
    'rgb(201, 203, 207)',  // Gray
    'rgb(255, 99, 71)',    // Red
    'rgb(50, 205, 50)',    // Green
    'rgb(30, 144, 255)'    // Dodger Blue
];

// Add color management system
const metricColorMap = new Map(); // Maps metric names to their assigned colors

// Function to get a unique color for a metric
function getMetricColor(metricName) {
    // If the metric already has a color assigned, return it
    if (metricColorMap.has(metricName)) {
        return metricColorMap.get(metricName);
    }

    // Find the first unused color
    const usedColors = new Set(metricColorMap.values());
    const availableColor = metricColors.find(color => !usedColors.has(color));

    if (availableColor) {
        // If we found an unused color, assign it
        metricColorMap.set(metricName, availableColor);
        return availableColor;
    } else {
        // If we ran out of predefined colors, generate a new one
        const newColor = generateDistinctColor(usedColors);
        metricColorMap.set(metricName, newColor);
        return newColor;
    }
}

// Function to generate a distinct color that's not in the used colors set
function generateDistinctColor(usedColors) {
    const hue = Math.floor(Math.random() * 360);
    const saturation = 70 + Math.floor(Math.random() * 30); // 70-100%
    const lightness = 45 + Math.floor(Math.random() * 10);  // 45-55%
    
    const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    
    // If this color is too similar to an existing one, try again
    if (isColorTooSimilar(color, usedColors)) {
        return generateDistinctColor(usedColors);
    }
    
    return color;
}

// Function to check if a color is too similar to existing colors
function isColorTooSimilar(color, usedColors) {
    const [h, s, l] = color.match(/\d+/g).map(Number);
    
    for (const usedColor of usedColors) {
        const [uh, us, ul] = usedColor.match(/\d+/g).map(Number);
        
        // Check if colors are too similar in hue, saturation, or lightness
        if (Math.abs(h - uh) < 30 && 
            Math.abs(s - us) < 20 && 
            Math.abs(l - ul) < 10) {
            return true;
        }
    }
    
    return false;
}

// Time window state
let timeWindowState = {
    start: null,
    end: null,
    isLive: true,
    
    scrollPosition: 50
};

// Initialize chart
const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [],
        datasets: []
    },
    options: {
        parsing: false,
        responsive: true,
        maintainAspectRatio: false,
        

        elements: {
            point: {
                radius: 0 // default to disabled in all datasets
            }
        },

        // normalized for more performance
        normalized: true, 
        layout: {
            padding: {
                top: 10,
                right: 10,
                bottom: 10,
                left: 10
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: null,
                grid: {
                    color: function(context) {
                        return context.tick.value === 0 ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.05)';
                    }
                },
                ticks: {
                    color: function() {
                        return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666';
                    }
                }
            },
            x: {
                // normalized for more performance
                normalized: true, 
                type: 'time',
                time: {
                    unit: 'minute',
                    displayFormats: {
                        minute: 'HH:mm'
                    }
                },
                title: {
                    display: true,
                    text: 'Time',
                    color: function() {
                        return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666';
                    }
                },
                grid: {
                    color: function(context) {
                        return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)';
                    }
                },
                ticks: {
                    color: function() {
                        return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666';
                    },
                    autoSkip: false
                },
                min: null,
                max: null
            }
        },
        // animation: {
        //     duration: 0
        // },
        // turned off for more performance
        animation: false,
        plugins: {
            decimation: {
                enabled: true,
                algorithm: 'min-max',
            },
            legend: {
                display: false,
                position: 'top',
                align: 'start',
                labels: {
                    color: function() {
                        return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666';
                    }
                }
            },
            tooltip: {
                enabled: true,
                position: "nearest",
                mode: 'index',
                intersect: false,
                backgroundColor: function() {
                    return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)';
                },
                titleColor: function() {
                    return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#fff' : '#666';
                },
                bodyColor: function() {
                    return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#fff' : '#666';
                },
                borderColor: function() {
                    return document.documentElement.getAttribute('data-bs-theme') === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)';
                },
                callbacks: {
                    label: function(context) {
                        return `${context.dataset.label}: ${context.parsed.y}`;
                    }
                }
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        }
    }
});

// Function to update the time axis display based on the selected period
function updateTimeAxisDisplay() {
    if (maxDataPoints <= 30) { // 30 seconds or less
        chart.options.scales.x.time.unit = 'second';
        chart.options.scales.x.time.displayFormats = {
            second: 'HH:mm:ss'
        };
        chart.options.scales.x.time.stepSize = 1; // Show every second
        chart.options.scales.x.ticks.callback = function(value, index, ticks) {
            // Default callback for 1-second interval
            const date = new Date(value);
            return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
        };
    } else if (maxDataPoints <= 300) { // 5 minutes or less (1min, 3min, 5min)
        chart.options.scales.x.time.unit = 'second'; // Set unit back to second for granular ticks
        chart.options.scales.x.time.stepSize = undefined; // Let Chart.js determine second intervals
        chart.options.scales.x.time.displayFormats = {
            second: 'HH:mm' // Still show seconds in format
        };
        chart.options.scales.x.ticks.callback = function(value, index, ticks) {
            const date = new Date(value);
            if (date.getSeconds() % 5 === 0) { // Only show labels at 5-second marks
                return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
            }
            return null; // Hide other labels
        };
    } else { // 10 minutes, 15 minutes, 30 minutes
        chart.options.scales.x.time.unit = 'minute';
        chart.options.scales.x.time.displayFormats = {
            minute: 'HH:mm'
        };
        chart.options.scales.x.time.stepSize = 1; // Let Chart.js determine automatically
        // chart.options.scales.x.ticks.callback = function(value, index, ticks) {
        //     // Default callback for minute interval
        //     const date = new Date(value);
        //     return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
        // };
    }
    // chart.update();
}

// Function to add a new metric
function addMetric(name) {
    if (!allData[name]) {
        allData[name] = [];
        receivedMetrics.add(name); // Add to received metrics
        activeMetrics.add(name); // Add newly discovered metric to active metrics
        const color = getMetricColor(name);
        chart.data.datasets.push({
            label: name,
            data: [],
            borderColor: color,
            // tension: 0.1,
            // less tension for more performance
            tension: 0,
            fill: false,
            spanGaps: spanGapsValue,
        });
        updateMetricList(); // Update the metric list UI
        updateChartDatasets(); // Update the chart to include the new dataset
    }
}

// Function to update visible data points based on time range
function updateVisibleData() {
    const now = new Date();
    const timeWindow = maxDataPoints * 1000; // Convert seconds to milliseconds
    
    // Calculate the total available time range
    const allTimes = Object.values(allData).flatMap(data => data.map(d => d.x.getTime()));
    if (allTimes.length === 0) return;
    
    // const oldestTime = new Date(allTimes.reduce((min, time) => time < min ? time : min, allTimes[0]));
    // const newestTime = new Date(allTimes.reduce((max, time) => time > max ? time : max, allTimes[0]));

    const oldestTime = new Date(allTimes[0]);
    const newestTime = new Date(allTimes[allTimes.length-1])


    const totalTimeRange = newestTime.getTime() - oldestTime.getTime();
    
    if (timeWindowState.isLive) {
        // Live mode - show most recent data
        timeWindowState.end = now;
        timeWindowState.start = new Date(now.getTime() - timeWindow);
        
        timeWindowState.scrollPosition = 100;
        document.getElementById('timeScroll').value = 100;
    } else {
        // Historical mode - maintain the current window
        if (!timeWindowState.start || !timeWindowState.end) {
            timeWindowState.end = now;
            timeWindowState.start = new Date(now.getTime() - timeWindow);
        }
        
        // Update scroll position based on current view
        const currentScrollOffset = timeWindowState.start.getTime() - oldestTime.getTime();
        const newScrollPosition = Math.min(100, Math.max(0, (currentScrollOffset / (totalTimeRange - timeWindow)) * 100));
        timeWindowState.scrollPosition = newScrollPosition;
        document.getElementById('timeScroll').value = newScrollPosition;
        
        // Only follow latest data if we're in live mode
        
    }
    
    // Set the time axis range
    chart.options.scales.x.min = timeWindowState.start;
    chart.options.scales.x.max = timeWindowState.end;

    updateTimeAxisDisplay(); // Call new function to set initial axis display here

    // Update each dataset
    chart.data.datasets.forEach(dataset => {
        const metricName = dataset.label;
        if (allData[metricName]) {
            dataset.data = allData[metricName].filter(point => 
                // point.x >= timeWindowState.start && point.x <= timeWindowState.end
                point.x >= timeWindowState.start
            );
        }
    });
    
    // chart.update();
}

// Function to clear selection
function clearSelection() {
    selectionBox.style.display = 'none';
    document.getElementById('selectionStats').innerHTML = '<p class="text-muted">Select a region on the chart to view statistics</p>';
}

// Handle follow button
const followButton = document.getElementById('followButton');
followButton.addEventListener('click', function() {
    timeWindowState.isLive = true;
    
    timeWindowState.scrollPosition = 100;
    document.getElementById('timeScroll').value = 100;
    clearSelection();
    // updateVisibleData();
});

// Handle pause button
const pauseButton = document.getElementById('pauseButton');
pauseButton.addEventListener('click', function() {
    timeWindowState.isLive = false;
    
    // updateVisibleData();
});

// Handle time period selection
document.querySelectorAll('.btn-group .btn[data-period]').forEach(button => {
    button.addEventListener('click', function() {
        // Update active state
        document.querySelectorAll('.btn-group .btn[data-period]').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        
        // Update max data points
        maxDataPoints = parseInt(this.dataset.period);
        
        // Reset to live mode when changing time period
        timeWindowState.isLive = true;
        
        timeWindowState.scrollPosition = 100;
        document.getElementById('timeScroll').value = 100;
        clearSelection();
        
        // Update visible data
        // updateVisibleData();
        // updateTimeAxisDisplay(); // Update time axis display when period changes
    });
});

// Handle time scroll
const timeScroll = document.getElementById('timeScroll');
timeScroll.addEventListener('input', function() {
    const value = parseInt(this.value);
    timeWindowState.scrollPosition = value;
    timeWindowState.isLive = false;
    
    clearSelection();
    
    // Calculate new time window based on scroll position
    const allTimes = Object.values(allData).flatMap(data => data.map(d => d.x.getTime()));
    if (allTimes.length === 0) return;
    
    // const oldestTime = new Date(allTimes.reduce((min, time) => time < min ? time : min, allTimes[0]));
    // const newestTime = new Date(allTimes.reduce((max, time) => time > max ? time : max, allTimes[0]));

    const oldestTime = new Date(allTimes[0]);
    const newestTime = new Date(allTimes[allTimes.length-1])


    const totalTimeRange = newestTime.getTime() - oldestTime.getTime();
    const timeWindow = maxDataPoints * 1000;
    
    const scrollOffset = (totalTimeRange - timeWindow) * (value / 100);
    timeWindowState.start = new Date(oldestTime.getTime() + scrollOffset);
    timeWindowState.end = new Date(timeWindowState.start.getTime() + timeWindow);
    
    // updateVisibleData();
});

// Handle incoming events
eventSource.addEventListener('metric', function(e) {
    const event = JSON.parse(e.data);
    const timestamp = new Date(event.timestamp);
    
    // Add new data point for the specific metric
    const newPoint = {
        x: timestamp,
        y: event.value
    };
    
    // Initialize metric data if it doesn't exist
    if (!allData[event.name]) {
        addMetric(event.name);
    }
    
    allData[event.name].push(newPoint);
    
    // Update visible data
    // Removed: updateVisibleData();
});

// Add a global interval for chart updates for more performance
setInterval(() => {
    updateVisibleData();
    chart.update();
}, 1000); // Update every 250 milliseconds

// Function to update the metric list
function updateMetricList() {
    const metricList = document.getElementById('metricList');
    metricList.innerHTML = '';
    
    // Show all received metrics, regardless of active state
    Array.from(receivedMetrics).sort().forEach(metric => {
        const color = getMetricColor(metric);
        const isActive = activeMetrics.has(metric);
        
        const item = document.createElement('a');
        item.href = '#';
        item.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-center`;
        item.style.borderLeft = `4px solid ${color}`;
        item.innerHTML = `
            <span class="${isActive ? 'text-primary' : ''}">${metric}</span>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" 
                    id="toggle-${metric}" ${isActive ? 'checked' : ''}>
            </div>
        `;
        
        const toggle = item.querySelector(`#toggle-${metric}`);
        toggle.addEventListener('change', function() {
            const metricName = item.querySelector('span');
            if (this.checked) {
                activeMetrics.add(metric);
                metricName.classList.add('text-primary');
            } else {
                activeMetrics.delete(metric);
                metricName.classList.remove('text-primary');
            }
            updateChartDatasets();
        });
        
        metricList.appendChild(item);
    });
}

// Add event listeners for enable/disable all buttons
document.getElementById('enableAllMetrics').addEventListener('click', function() {
    // Enable all received metrics
    receivedMetrics.forEach(metric => {
        activeMetrics.add(metric);
    });
    updateMetricList();
    updateChartDatasets();
});

document.getElementById('disableAllMetrics').addEventListener('click', function() {
    // Clear active metrics but keep them in received metrics
    activeMetrics.clear();
    // Force update the metric list to ensure all metrics are shown
    const metricList = document.getElementById('metricList');
    metricList.innerHTML = '';
    Array.from(receivedMetrics).sort().forEach(metric => {
        const color = getMetricColor(metric);
        const item = document.createElement('a');
        item.href = '#';
        item.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-center`;
        item.style.borderLeft = `4px solid ${color}`;
        item.innerHTML = `
            <span>${metric}</span>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" 
                    id="toggle-${metric}">
            </div>
        `;
        
        const toggle = item.querySelector(`#toggle-${metric}`);
        toggle.addEventListener('change', function() {
            const metricName = item.querySelector('span');
            if (this.checked) {
                activeMetrics.add(metric);
                metricName.classList.add('text-primary');
            } else {
                activeMetrics.delete(metric);
                metricName.classList.remove('text-primary');
            }
            updateChartDatasets();
        });
        
        metricList.appendChild(item);
    });
    updateChartDatasets();
});

// Function to update chart datasets based on active metrics
function updateChartDatasets() {
    // Remove datasets for inactive metrics
    chart.data.datasets = chart.data.datasets.filter(dataset => 
        activeMetrics.has(dataset.label)
    );
    
    // Add datasets for active metrics that don't have one yet
    activeMetrics.forEach(metric => {
        if (!chart.data.datasets.find(ds => ds.label === metric)) {
            const color = getMetricColor(metric);
            chart.data.datasets.push({
                label: metric,
                data: allData[metric] || [],
                borderColor: color,
                tension: 0.1,
                fill: false,
                spanGaps: spanGapsValue,
                parsing: false,
                
            });
        }
    });
    
    // chart.update();
}

// Handle connection errors
eventSource.onerror = function(error) {
    console.error('SSE Error:', error);
    eventSource.close();
};

// Initialize chart with historical data
// Removed the direct historicalData initialization from Jinja2 context

// Fetch historical data asynchronously
async function loadHistoricalData() {
    try {
        const response = await fetch('/historical_data');
        const historicalData = await response.json();

        historicalData.forEach(event => {
            const timestamp = new Date(event.timestamp);
            const newPoint = {
                x: timestamp,
                y: event.value
            };
            
            if (!allData[event.name]) {
                allData[event.name] = [];
                receivedMetrics.add(event.name);
                activeMetrics.add(event.name);
                const color = getMetricColor(event.name);
                chart.data.datasets.push({
                    label: event.name,
                    data: [],
                    borderColor: color,
                    tension: 0,
                    fill: false,
                    spanGaps: spanGapsValue,
                    
                });
            }
            allData[event.name].push(newPoint);
        });

        // Ensure all data is sorted by time after loading historical data
        for (const metricName in allData) {
            allData[metricName].sort((a, b) => a.x.getTime() - b.x.getTime());
        }

        // updateMetricList();
        // updateChartDatasets();
        // updateVisibleData();
        // updateTimeAxisDisplay();

    } catch (error) {
        console.error('Error fetching historical data:', error);
    }
}

// Call the function to load historical data when the page loads
loadHistoricalData();

// Add selection and cursor elements
const chartContainer = document.querySelector('.position-relative');
const selectionBox = document.createElement('div');
selectionBox.className = 'chart-selection';
selectionBox.style.display = 'none';
chartContainer.appendChild(selectionBox);

const cursorLine = document.createElement('div');
cursorLine.className = 'chart-cursor';
chartContainer.appendChild(cursorLine);

// Selection state
let isSelecting = false;
let selectionStart = null;
let selectionEnd = null;

// Function to calculate statistics for a time range
function calculateStats(startTime, endTime) {
    const stats = {};
    
    activeMetrics.forEach(metric => {
        const values = allData[metric]
            .filter(point => point.x >= startTime && point.x <= endTime)
            .map(point => point.y);
            
        if (values.length > 0) {
            stats[metric] = {
                min: values.reduce((a, b) => Math.min(a, b), values[0]),
                max: values.reduce((a, b) => Math.max(a, b), values[0]),
                avg: values.reduce((a, b) => a + b, 0) / values.length,
                median: calculateMedian(values)
            };
        }
    });
    
    return stats;
}

// Function to calculate median
function calculateMedian(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
        return (sorted[middle - 1] + sorted[middle]) / 2;
    }
    
    return sorted[middle];
}

// Function to update selection statistics display
function updateSelectionStats(startTime, endTime) {
    const stats = calculateStats(startTime, endTime);
    const statsContainer = document.getElementById('selectionStats');
    
    if (Object.keys(stats).length === 0) {
        statsContainer.innerHTML = '<p class="text-muted">No data in selected range</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>Metric</th><th>Min</th><th>Max</th><th>Avg</th><th>Median</th></tr></thead><tbody>';
    
    Object.entries(stats).forEach(([metric, values]) => {
        html += `<tr>
            <td>${metric}</td>
            <td>${values.min.toFixed(2)}</td>
            <td>${values.max.toFixed(2)}</td>
            <td>${values.avg.toFixed(2)}</td>
            <td>${values.median.toFixed(2)}</td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    statsContainer.innerHTML = html;
}

// Handle mouse events for selection
chartContainer.addEventListener('mousedown', function(e) {
    if (!timeWindowState.isLive) {
        isSelecting = true;
        const rect = chartContainer.getBoundingClientRect();
        selectionStart = e.clientX - rect.left;
        selectionBox.style.left = selectionStart + 'px';
        selectionBox.style.top = '0';
        selectionBox.style.height = rect.height + 'px';
        selectionBox.style.width = '0';
        selectionBox.style.display = 'block';
    }
});

chartContainer.addEventListener('mousemove', function(e) {
    const rect = chartContainer.getBoundingClientRect();
    const x = e.clientX - rect.left;
    
    // Update cursor line
    cursorLine.style.left = x + 'px';
    cursorLine.style.top = '0';
    cursorLine.style.height = rect.height + 'px';
    cursorLine.style.display = 'block';
    
    // Update timestamp display
    const timestamp = chart.scales.x.getValueForPixel(x);
    if (timestamp) {
        const date = new Date(timestamp);
        const timestampDisplay = document.getElementById('cursorTimestamp');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
        timestampDisplay.textContent = `${hours}:${minutes}:${seconds}.${milliseconds}`;
        timestampDisplay.style.display = 'block';
    }
    
    // Update selection box
    if (isSelecting) {
        const width = x - selectionStart;
        selectionBox.style.width = Math.abs(width) + 'px';
        selectionBox.style.left = (width < 0 ? x : selectionStart) + 'px';
    }
});

chartContainer.addEventListener('mouseup', function(e) {
    if (isSelecting) {
        isSelecting = false;
        const rect = chartContainer.getBoundingClientRect();
        selectionEnd = e.clientX - rect.left;
        
        // Convert pixel positions to timestamps
        const startX = Math.min(selectionStart, selectionEnd);
        const endX = Math.max(selectionStart, selectionEnd);
        
        const startTime = chart.scales.x.getValueForPixel(startX);
        const endTime = chart.scales.x.getValueForPixel(endX);
        
        updateSelectionStats(startTime, endTime);
    }
});

chartContainer.addEventListener('mouseleave', function() {
    cursorLine.style.display = 'none';
    document.getElementById('cursorTimestamp').style.display = 'none';
    if (isSelecting) {
        isSelecting = false;
        selectionBox.style.display = 'none';
    }
});

// Log streaming functionality
let logEventSource;
const logTableBody = document.getElementById('logTableBody');
const maxLogs = 1000; // Maximum number of logs to keep in memory

function addLogEntry(log) {
    const row = document.createElement('tr');
    row.className = `log-level-${log.level}`;
    
    const timestampCell = document.createElement('td');
    timestampCell.textContent = new Date(log.timestamp).toLocaleString();
    
    const levelCell = document.createElement('td');
    levelCell.textContent = log.level;
    
    const threadCell = document.createElement('td');
    threadCell.textContent = log.thread;
    threadCell.style.fontFamily = 'monospace';
    threadCell.style.fontSize = '0.9em';
    
    const messageCell = document.createElement('td');
    messageCell.className = 'log-entry';
    messageCell.textContent = log.message;
    
    row.appendChild(timestampCell);
    row.appendChild(levelCell);
    row.appendChild(threadCell);
    row.appendChild(messageCell);
    
    logTableBody.insertBefore(row, logTableBody.firstChild);
    
    // Remove oldest log if we exceed maxLogs
    if (logTableBody.children.length > maxLogs) {
        logTableBody.removeChild(logTableBody.lastChild);
    }
}

function clearLogs() {
    logTableBody.innerHTML = '';
}

function scrollToBottom() {
    const logPanel = document.getElementById('logPanel');
    if (logPanel.classList.contains('show')) {
        const tableContainer = logPanel.querySelector('.table-responsive');
        tableContainer.scrollTop = tableContainer.scrollHeight;
    }
}

function scrollToTop() {
    const logPanel = document.getElementById('logPanel');
    if (logPanel.classList.contains('show')) {
        const tableContainer = logPanel.querySelector('.table-responsive');
        tableContainer.scrollTop = 0;
    }
}

async function loadHistoricalLogs() {
    try {
        const response = await fetch('/log_historical');
        const logs = await response.json();
        logs.forEach(log => addLogEntry(log));
    } catch (error) {
        console.error('Error loading historical logs:', error);
    }
}

function startLogEventSource() {
    if (logEventSource) {
        logEventSource.close();
    }

    logEventSource = new EventSource('/log_current');
    
    logEventSource.addEventListener('log', (event) => {
        const log = JSON.parse(event.data);
        addLogEntry(log);
    });

    logEventSource.addEventListener('error', (error) => {
        console.error('SSE Error:', error);
        // Attempt to reconnect after 5 seconds
        setTimeout(startLogEventSource, 5000);
    });
}

// Initialize log streaming
loadHistoricalLogs();
startLogEventSource();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (logEventSource) {
        logEventSource.close();
    }
});
</script>
{% endblock %} 