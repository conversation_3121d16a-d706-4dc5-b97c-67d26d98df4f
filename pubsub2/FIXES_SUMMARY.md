# Chart Performance Fixes Summary

## Issues Addressed

### 1. Chart.js Errors (TypeError: Cannot read properties of undefined)
**Problem**: Chart.js was trying to access undefined data points, causing crashes when switching to longer time intervals.

**Root Cause**: 
- Invalid data points in datasets (null, undefined, or malformed data)
- Improper data formatting for Chart.js
- Missing data validation

**Fixes Applied**:
- ✅ Added comprehensive data validation in `updateVisibleData()`
- ✅ Filter out invalid data points before passing to Chart.js
- ✅ Ensure all data points have valid `x` (timestamp) and `y` (value) properties
- ✅ Convert timestamps to milliseconds for Chart.js compatibility
- ✅ Added error handling and debug logging for problematic datasets

### 2. Data Persistence Across Time Intervals
**Problem**: Data was being lost when switching between time intervals, preventing scrolling back to view historical data.

**Root Cause**: 
- Circular buffer was too small (50K points)
- Data was being filtered out permanently instead of just for display

**Fixes Applied**:
- ✅ Increased circular buffer size to 500K points per metric
- ✅ Added `getAllData()` method to DataManager for complete data access
- ✅ Separated data storage from data display logic
- ✅ Improved time window calculation to handle edge cases

### 3. Min-Max Decimation Implementation
**Problem**: Chart was using LTTB decimation which doesn't preserve local extremes.

**Root Cause**: 
- Chart.js decimation was set to 'lttb' algorithm
- No custom decimation for preserving min/max values

**Fixes Applied**:
- ✅ Changed Chart.js decimation to 'min-max' algorithm
- ✅ Implemented custom `applyMinMaxDecimation()` method
- ✅ Increased decimation threshold to 2000 points for better quality
- ✅ Preserve both min and max values in each bucket

### 4. Data Validation and Error Handling
**Problem**: Invalid data from SSE or historical loading was causing chart crashes.

**Root Cause**: 
- No validation of incoming data
- Malformed timestamps or values
- Missing error handling

**Fixes Applied**:
- ✅ Added try-catch blocks around data processing
- ✅ Validate timestamps and numeric values
- ✅ Filter out NaN, null, and undefined values
- ✅ Added warning logs for invalid data points

## Technical Implementation Details

### DataManager Improvements
```javascript
// Enhanced data validation
getDataInRange(metricName, startTime, endTime) {
    // ... existing code ...
    const result = [];
    for (let i = startIdx; i <= endIdx && i < data.length; i++) {
        if (data[i] && data[i].x && data[i].y !== undefined) {
            result.push(data[i]);
        }
    }
    return result;
}

// New method for complete data access
getAllData(metricName) {
    if (!this.metrics.has(metricName)) {
        return [];
    }
    const buffer = this.metrics.get(metricName);
    return buffer.toArray().filter(point => 
        point && point.x && point.y !== undefined
    );
}
```

### ChartManager Improvements
```javascript
// Enhanced data formatting and validation
updateVisibleData(startTime, endTime) {
    this.chart.data.datasets.forEach(dataset => {
        const formattedData = rangeData
            .filter(point => {
                return point && 
                       point.x && 
                       point.y !== undefined && 
                       point.y !== null && 
                       !isNaN(point.y) &&
                       point.x instanceof Date &&
                       !isNaN(point.x.getTime());
            })
            .sort((a, b) => a.x.getTime() - b.x.getTime())
            .map(point => ({
                x: point.x.getTime(), // Use timestamp for Chart.js
                y: Number(point.y)
            }));
        
        // Apply custom min-max decimation if needed
        if (formattedData.length > 2000) {
            dataset.data = this.applyMinMaxDecimation(formattedData, 2000);
        } else {
            dataset.data = formattedData;
        }
    });
}

// Custom min-max decimation preserving extremes
applyMinMaxDecimation(data, maxPoints) {
    if (data.length <= maxPoints) return data;
    
    const result = [];
    const bucketSize = Math.ceil(data.length / maxPoints);
    
    for (let i = 0; i < data.length; i += bucketSize) {
        const bucket = data.slice(i, Math.min(i + bucketSize, data.length));
        
        if (bucket.length > 1) {
            // Find min and max in bucket
            let min = bucket[0];
            let max = bucket[0];
            
            for (let j = 1; j < bucket.length; j++) {
                if (bucket[j].y < min.y) min = bucket[j];
                if (bucket[j].y > max.y) max = bucket[j];
            }
            
            // Add both min and max (in chronological order)
            if (min.x < max.x) {
                result.push(min);
                if (min !== max) result.push(max);
            } else {
                result.push(max);
                if (min !== max) result.push(min);
            }
        } else if (bucket.length === 1) {
            result.push(bucket[0]);
        }
    }
    
    return result;
}
```

### Chart.js Configuration Updates
```javascript
plugins: {
    decimation: {
        enabled: true,
        algorithm: 'min-max', // Changed from 'lttb'
        samples: 2000,        // Increased from 1000
        threshold: 1000       // Only decimate when > 1000 points
    }
}
```

## Performance Optimizations Maintained

### Memory Management
- ✅ Circular buffers prevent unlimited memory growth
- ✅ Increased buffer size (500K) for better data retention
- ✅ Efficient time-based indexing for fast queries

### Rendering Performance
- ✅ Throttled chart updates (100ms minimum)
- ✅ Disabled animations for better performance
- ✅ Min-max decimation preserves important features
- ✅ RequestAnimationFrame for smooth updates

### Network Efficiency
- ✅ Server-side event batching maintained
- ✅ Paginated historical data loading
- ✅ Efficient JSON serialization

## Testing Recommendations

### Test Cases to Verify Fixes
1. **Switch between time intervals** (30s → 1m → 3m → 5m → 30m)
   - Should not produce Chart.js errors
   - Should maintain all historical data
   - Should preserve min/max values in decimated view

2. **Scroll through historical data**
   - Use time scroll bar to navigate
   - Verify data is available across entire time range
   - Check that extremes are visible in longer intervals

3. **High-frequency data ingestion**
   - Generate rapid data updates (>100 Hz)
   - Verify chart remains responsive
   - Check memory usage stays constant

4. **Large dataset loading**
   - Load historical data with >100K points
   - Verify smooth loading and rendering
   - Test selection and statistics features

### Debug Mode
Enable debug mode in ChartManager constructor:
```javascript
const chartManager = new ChartManager('metricChart', dataManager, colorManager, true);
```

This will provide additional console logging for troubleshooting.

## Files Modified

### Core Fixes
- `pubsub2/static/js/chart-manager.js` - Enhanced data validation and min-max decimation
- `pubsub2/static/js/data-manager.js` - Improved data storage and retrieval
- `pubsub2/templates/index.html` - Updated application initialization
- `pubsub2/server_component.py` - Fixed SSE loop condition

### New Features
- Custom min-max decimation algorithm
- Enhanced data validation pipeline
- Debug mode for troubleshooting
- Improved error handling and logging

## Expected Results

After applying these fixes:
- ✅ No more Chart.js TypeError crashes
- ✅ Smooth switching between all time intervals
- ✅ Complete data retention for scrolling/zooming
- ✅ Local min/max values preserved in decimated views
- ✅ Maintained 60 FPS performance
- ✅ Robust error handling for invalid data
