"""
Modified version of main.py using the RobustMQTTClient.

This demonstrates how to integrate the robust MQTT client into your existing ROS2 node.
"""

import rclpy
from rclpy.node import Node
from vision_msgs.msg import Detection2DArray
import orjson
import time
import logging
from iot import RobustMQTTC<PERSON>, MQTTConfig, ProcessingConfig
from metric_archiver import MetricArchiver

# Configuration constants
ENDPOINT = "a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com"
CLIENT_ID = "drone-1"
TENANT = "uvionix"
PATH_TO_CERT = "drones/drone-1/certificate.pem"
PATH_TO_KEY = "drones/drone-1/private.key"
PATH_TO_ROOT = "root-CA.crt"
METRIC_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/metrics"
COMMAND_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/commands"
BARCODE_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/barcode"


class RobustPubSub2(Node):
    """
    ROS2 node with robust MQTT client integration.
    
    This replaces the original PubSub2 class with a more robust implementation
    that uses SQLite-based message buffering and automatic retry capabilities.
    """

    def __init__(self):
        super().__init__('robust_pubsub2')
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = self.get_logger()
        
        self.msgs_sent = 0

        # self.archiver = MetricArchiver()
        
        # Configure robust MQTT client
        mqtt_config = MQTTConfig(
            endpoint=ENDPOINT,
            client_id=CLIENT_ID,
            cert_filepath=PATH_TO_CERT,
            pri_key_filepath=PATH_TO_KEY,
            ca_filepath=PATH_TO_ROOT,
            clean_session=False,
            keep_alive_secs=10,
            ping_timeout_ms=3000,
            protocol_operation_timeout_ms=5000,
            tcp_connect_timeout_ms=3000,
            reconnect_min_timeout_secs=1,
            reconnect_max_timeout_secs=5
        )

        # Configure message processing behavior
        processing_config = ProcessingConfig(
            processing_interval=1.0,  # Process messages every second
            vacuum_interval=60.0     # Vacuum database every minute
        )

        # Create and start robust MQTT client
        self.mqtt_client = RobustMQTTClient(
            mqtt_config=mqtt_config,
            processing_config=processing_config,
            db_path="ros_mqtt_buffer.db"
        )
        
        self.mqtt_client.start()
        self.logger.info("Robust MQTT client started")
        
        # Subscribe to MQTT command topic
        # self.mqtt_client.subscribe(COMMAND_TOPIC, self.mqtt_listener_callback)
        
        # Create ROS2 subscriber
        self.subscription = self.create_subscription(
            msg_type=Detection2DArray,
            topic='infer/yolov8/output/detections',
            callback=self.ros_listener_callback,
            qos_profile=10
        )
        
        self.subscription  # prevent unused variable warning
        self.logger.info('Robust subscriber node started and listening to /infer/yolov8/output/detections')
        
        # Create timer to periodically log status
        self.status_timer = self.create_timer(5.0, self.log_status)

    def mqtt_listener_callback(self, topic: str, payload: bytes):
        """Handle incoming MQTT messages."""
        try:
            data = orjson.loads(payload)
            self.logger.info(f"Received message on topic {topic}: {data}")
            # Handle the command here
        except Exception as e:
            self.logger.error(f"Error processing MQTT message: {e}")

    def ros_listener_callback(self, msg):
        """Handle incoming ROS2 detection messages."""
        try:
            # Extract metric value (same as original)
            metric_value = msg.detections[0].bbox.size_x + msg.detections[0].bbox.center.position.x
            
            ts = time.time()

            payoad = {
                "drone_id": 1,
                "timestamp": ts,
                "name": "foobar_metric",
                "value": metric_value,
            }
            
            # Create AWS message
            aws_message = {
                "type": "DB",
                "tenant": TENANT,
                **payoad
            }

            # Publish via robust MQTT client (returns message ID from database)
            # message_id = self.mqtt_client.publish(METRIC_TOPIC, orjson.dumps(aws_message).decode('utf-8'))
            message_id = self.mqtt_client.publish(METRIC_TOPIC, orjson.dumps(aws_message))
            # self.archiver.archive_metric(
            #     aws_message
            # )

            self.msgs_sent += 1
            # self.logger.info(f"Sent metric to topic 'foobar_metric': {metric_value} (Message ID: {message_id})")
            
        except Exception as e:
            self.logger.error(f"Error processing ROS message: {e}")

    def log_status(self):
        """Periodically log connection and buffer status."""
        status = self.mqtt_client.get_connection_status()
        self.logger.info(f"MQTT: Connected={status['connected']}, "
                        f"Pending={status['pending_messages']}, "
                        f"Messages sent={self.msgs_sent}")

    def destroy_node(self):
        """Clean shutdown of the node."""
        self.logger.info("Shutting down robust MQTT client...")
        self.mqtt_client.stop()
        super().destroy_node()


def main(args=None):
    """Main function."""
    # Initialize ROS2
    rclpy.init(args=args)
    
    # Create node
    node = RobustPubSub2()
    
    try:
        # Spin the node
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # Clean shutdown
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
