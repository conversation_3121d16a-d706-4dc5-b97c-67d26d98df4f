"""
Modified version of main.py using the RobustMQTTClient.

This demonstrates how to integrate the robust MQTT client into your existing ROS2 node.
"""
import logging

logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    force=True
)

import orjson
import time

import threading
import sys
import math
from iot import RobustMQTTClient, MQTTConfig, ProcessingConfig
from dummy import DummyMQTTClient


# Configuration constants
ENDPOINT = "a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com"
CLIENT_ID = "drone-1"
TENANT = "uvionix"
PATH_TO_CERT = "drones/drone-1/certificate.pem"
PATH_TO_KEY = "drones/drone-1/private.key"
PATH_TO_ROOT = "root-CA.crt"
METRIC_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/metrics"
COMMAND_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/commands"
BARCODE_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/barcode"



class Benchmark:
    """
    ROS2 node with robust MQTT client integration.
    
    This replaces the original PubSub2 class with a more robust implementation
    that uses SQLite-based message buffering and automatic retry capabilities.
    """

    def __init__(self):
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger()
        
        self.msgs_sent = 0
        self.running = True

        # self.max_messages = 600
        self.max_messages = 100 * 60 * 1
        self.num_values = 1500
        self.rate = 100

        self.target_rps = self.num_values * self.rate
        self.target_time = self.max_messages / self.rate

        print(f"time {self.target_time}s RPS: {self.target_rps}")
        
        self.initial_value = 0

        self.mqtt_client = DummyMQTTClient(
            clear=True,
            archive=False,
            db_path='synthetic.db'
        )
        
        self.mqtt_client.start()
        self.logger.info("Robust MQTT client started")

    def get_value(self):
        # try:
        #     return self.initial_value
        # finally:
        #     self.initial_value += 1
        self.initial_value += 1
        return self.initial_value
    
    def generate_multirecord_message(self):

        ts = time.time()

        aws_message = {
            "type": "DB_MULTIRECORD",
            "tenant": TENANT,
            "drone_id": 1,
            'uuid': '108fc6b2-b85d-44f7-ade6-aaf42843d18f', 
            'tenantNs': 'uvionix', 
            'topicNamespace': 'drone-1', 
            'clientId': 'drone-1', 
            'principalId': '2fa6075705d4920c5e2c83751db2fd325b517c819aa5f519c0414e531143d355',
            'values' : [
                {
                    # 'value': self.get_value(),
                    'value': (i * 2.5) + math.sin(ts / 10.0 + i * (2 * math.pi / self.num_values)),
                    'timestamp': ts,
                    'name': f'mrecord_{i}'
                }
                
                for i in range(self.num_values)
            ]
        }

        return orjson.dumps(aws_message)

    def generate_multivalue_message(self):
        # Create AWS message
        aws_message = {
            "type": "DB_MULTIVALUE",
            "tenant": TENANT,
            "drone_id": 1,
            "timestamp": time.time(),
            'values': {
                f'latest_{i}': self.get_value()
                for i in range(self.num_values)
            }
        }

        return orjson.dumps(aws_message)

    def log_status(self):
        """Periodically log connection and buffer status."""
        status = self.mqtt_client.get_connection_status()
        self.logger.info(f"MQTT: Connected={status['connected']} "
                        f"Pending={status['pending_messages']} "
                        f"Messages={self.msgs_sent} "
                        f"MPS={status['rate']}"
                        )

    def message_sender_thread(self):
        """Thread function that continuously sends messages."""
        while self.running:
            # message = self.generate_multivalue_message()
            message = self.generate_multirecord_message()

            # message_bytes = len(message)
            # self.logger.info(f"Message size: {message_bytes} bytes")
        
            message_id = self.mqtt_client.publish(METRIC_TOPIC, message)
            time.sleep(1/self.rate)

            self.msgs_sent += 1

            if self.msgs_sent >= self.max_messages:
                # let residual messages get sent

                self.running = False
                # while self.mqtt_client.get_pending_message_count() > 0:
                #     print("messages pending, waiting")
                #     time.sleep(5)

    def status_logger_thread(self):
        """Thread function that periodically logs connection status."""
        while self.running:
            self.log_status()
            time.sleep(5)  # Log status every 5 seconds

    def run(self):
        """Start the message sender and status logger threads."""
        # Create and start threads
        sender_thread = threading.Thread(target=self.message_sender_thread, name="MessageGenerator")
        # logger_thread = threading.Thread(target=self.status_logger_thread)
        
        sender_thread.start()
        # logger_thread.start()
        
        try:
            # Keep main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.running = False
            sender_thread.join()
            # logger_thread.join()
            self.mqtt_client.stop()

    def __del__(self):
        self.mqtt_client.stop()
        # sys.exit(0)

def main(args=None):
    """Main function."""
    
    # Create node
    node = Benchmark()
    
    node.run()


if __name__ == '__main__':
    main()
