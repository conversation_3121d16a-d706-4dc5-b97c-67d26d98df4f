html, body {
    height: 100%;
    overflow-x: hidden;
}

.container-fluid {
    height: calc(100vh - 56px); /* Subtract navbar height */
    padding-top: 1rem;
    padding-bottom: 1rem;
    overflow-x: hidden;
}

.row {
    min-height: 0;
    display: flex;
    flex-wrap: nowrap;
    margin-right: 0;
    margin-left: 0;
}

.card {
    min-height: 0;
}

.btn-group {
    flex-wrap: nowrap;
}

.btn-group .btn {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.9rem;
}

/* Add styles for metric control buttons */
#enableAllMetrics, #disableAllMetrics {
    padding: 0.1rem 0.3rem;
    font-size: 0.75rem;
}

#enableAllMetrics .bi, #disableAllMetrics .bi {
    font-size: 0.75rem;
}

#cursorTimestamp {
    font-family: monospace;
}

@media (max-width: 1400px) {
    .btn-group .btn {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
}

.chart-selection {
    position: absolute;
    background-color: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    pointer-events: none;
}

.chart-cursor {
    position: absolute;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: none;
    display: none;
}

[data-bs-theme="dark"] .chart-cursor {
    background-color: rgba(255, 255, 255, 0.3);
}

.list-group-item {
    padding: 0;
}

#selectionStats p,
#selectionStats .table {
    margin-bottom: 0;
}

#selectionStats .table th,
#selectionStats .table td {
    padding: 0.2rem 0.5rem;
}

.col-md-3 .card-body {
    padding: 0;
    min-height: 0;
    flex-shrink: 1;
    flex-basis: 0;
}

.list-group {
    min-height: 0;
}

#selectionStats {
    /* No height constraint here, let flex-grow-1 handle it */
}

/* Log table styles */
.log-table {
    font-family: monospace;
    font-size: 0.9em;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.log-table th,
.log-table td {
    padding: 0.25rem 0.5rem;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
}

.log-entry {
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 0;
}

.table-responsive {
    overflow-x: hidden !important;
}

.card-body {
    overflow: hidden;
}

.log-level-DEBUG { color: #6c757d; }
.log-level-INFO { color: #0d6efd; }
.log-level-WARNING { color: #ffc107; }
.log-level-ERROR { color: #dc3545; }
.log-level-CRITICAL { color: #dc3545; font-weight: bold; }

[data-bs-theme="dark"] .log-level-DEBUG { color: #adb5bd; }
[data-bs-theme="dark"] .log-level-INFO { color: #6ea8fe; }
[data-bs-theme="dark"] .log-level-WARNING { color: #ffc107; }
[data-bs-theme="dark"] .log-level-ERROR { color: #ff6b6b; }
[data-bs-theme="dark"] .log-level-CRITICAL { color: #ff6b6b; font-weight: bold; }
[data-bs-theme="dark"] .sticky-top {
    background-color: #2b3035 !important;
} 