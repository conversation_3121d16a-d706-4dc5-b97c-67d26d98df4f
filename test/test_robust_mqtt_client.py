"""
Unit tests for the RobustMQTTClient class.
"""

import unittest
import tempfile
import os
import time
import sqlite3
import threading
import logging
from unittest.mock import Mock, patch
from pubsub2.robust_mqtt_client import RobustMQTT<PERSON>lient, MQTTConfig, ProcessingConfig


class TestRobustMQTTClient(unittest.TestCase):
    """Test cases for RobustMQTTClient."""

    def setUp(self):
        """Set up test fixtures."""
        # Create temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()

        # Mock MQTT configuration
        self.mqtt_config = MQTTConfig(
            endpoint="test-endpoint.amazonaws.com",
            client_id="test-client",
            cert_filepath="test-cert.pem",
            pri_key_filepath="test-key.pem",
            ca_filepath="test-ca.crt"
        )

        # Test processing configuration
        self.processing_config = ProcessingConfig(
            processing_interval=0.1,
            vacuum_interval=1.0
        )

        # Setup logging
        logging.basicConfig(level=logging.DEBUG)

    def tearDown(self):
        """Clean up test fixtures."""
        # Remove temporary database file
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_initialization(self, mock_builder):
        """Test client initialization."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Verify database initialization
        self.assertTrue(os.path.exists(self.temp_db.name))
        self.assertIsNotNone(client)  # Ensure client was created

        # Check database schema
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='message_buffer'")
        self.assertIsNotNone(cursor.fetchone())

        # Check that the correct columns exist (no UUID columns)
        cursor.execute("PRAGMA table_info(message_buffer)")
        columns = [row[1] for row in cursor.fetchall()]
        expected_columns = ['id', 'topic', 'payload', 'qos', 'timestamp', 'is_processing', 'last_attempt_at']
        for col in expected_columns:
            self.assertIn(col, columns)

        # Ensure no UUID-related columns exist
        self.assertNotIn('message_uuid', columns)
        self.assertNotIn('uuid', columns)

        conn.close()

        # Verify MQTT connection setup
        mock_builder.mtls_from_path.assert_called_once()

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_publish_stores_message_in_database(self, mock_builder):
        """Test that publishing stores message in database for background processing."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Publish message (this should always store in database first)
        message_id = client.publish("test/topic", "test payload data")

        # Verify message was stored in database
        self.assertEqual(client.get_pending_message_count(), 1)
        self.assertIsInstance(message_id, int)
        self.assertGreater(message_id, 0)

        # Verify message details in database
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        cursor.execute("SELECT id, topic, payload, qos FROM message_buffer WHERE id = ?", (message_id,))
        row = cursor.fetchone()
        conn.close()

        self.assertIsNotNone(row)
        self.assertEqual(row[0], message_id)
        self.assertEqual(row[1], "test/topic")
        self.assertEqual(row[2], "test payload data")
        self.assertEqual(row[3], 1)  # default QoS

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_publish_with_custom_qos(self, mock_builder):
        """Test publishing with custom QoS level."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Publish message with QoS 0
        message_id = client.publish("test/topic", "test payload", qos=0)

        # Verify message was stored with correct QoS
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        cursor.execute("SELECT qos FROM message_buffer WHERE id = ?", (message_id,))
        row = cursor.fetchone()
        conn.close()

        self.assertEqual(row[0], 0)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_message_processing_workflow(self, mock_builder):
        """Test the complete message processing workflow."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        # Mock successful publish
        mock_future = Mock()
        mock_future.result.return_value = None
        mock_connection.publish.return_value = (mock_future, 123)

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Simulate connected state
        client.is_connected.set()

        # Publish message
        message_id = client.publish("test/topic", "test payload")

        # Verify message was stored initially
        self.assertEqual(client.get_pending_message_count(), 1)
        self.assertIsInstance(message_id, int)  # Ensure message ID was returned

        # Manually trigger message processing
        client._process_pending_messages()

        # Give some time for async processing
        time.sleep(0.1)

        # Verify publish was attempted
        mock_connection.publish.assert_called_once()

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_context_manager(self, mock_builder):
        """Test context manager functionality."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        with RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        ) as client:
            self.assertTrue(client.threads_started)

        # Verify client was stopped
        self.assertFalse(client.threads_started)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_get_connection_status(self, mock_builder):
        """Test connection status reporting."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        status = client.get_connection_status()

        self.assertIn('connected', status)
        self.assertIn('pending_messages', status)
        self.assertIn('threads_started', status)
        self.assertIn('client_id', status)
        self.assertIn('endpoint', status)

        self.assertEqual(status['client_id'], self.mqtt_config.client_id)
        self.assertEqual(status['endpoint'], self.mqtt_config.endpoint)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_multiple_messages_ordering(self, mock_builder):
        """Test that multiple messages are processed in order."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Add multiple messages
        msg_id1 = client.publish("test/topic1", "message 1")
        time.sleep(0.01)  # Small delay to ensure different timestamps
        msg_id2 = client.publish("test/topic2", "message 2")
        time.sleep(0.01)
        msg_id3 = client.publish("test/topic3", "message 3")

        self.assertEqual(client.get_pending_message_count(), 3)

        # Verify messages are ordered by timestamp
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM message_buffer ORDER BY timestamp ASC")
        rows = cursor.fetchall()
        conn.close()

        self.assertEqual(len(rows), 3)
        self.assertEqual(rows[0][0], msg_id1)
        self.assertEqual(rows[1][0], msg_id2)
        self.assertEqual(rows[2][0], msg_id3)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_subscribe_when_connected(self, mock_builder):
        """Test subscribing to a topic when connected."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        # Mock successful subscribe
        mock_future = Mock()
        mock_future.result.return_value = None
        mock_connection.subscribe.return_value = (mock_future, 123)

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Simulate connected state
        client.is_connected.set()

        # Subscribe to topic
        callback = Mock()
        result = client.subscribe("test/topic", callback)

        self.assertTrue(result)
        mock_connection.subscribe.assert_called_once()

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_subscribe_when_disconnected(self, mock_builder):
        """Test subscribing to a topic when disconnected."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Ensure disconnected state
        client.is_connected.clear()

        # Subscribe to topic
        callback = Mock()
        result = client.subscribe("test/topic", callback)

        self.assertFalse(result)
        mock_connection.subscribe.assert_not_called()

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_unsubscribe_when_connected(self, mock_builder):
        """Test unsubscribing from a topic when connected."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        # Mock successful unsubscribe
        mock_future = Mock()
        mock_future.result.return_value = None
        mock_connection.unsubscribe.return_value = (mock_future, 123)

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Simulate connected state
        client.is_connected.set()

        # Unsubscribe from topic
        result = client.unsubscribe("test/topic")

        self.assertTrue(result)
        mock_connection.unsubscribe.assert_called_once()

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_processing_config_usage(self, mock_builder):
        """Test that processing configuration is properly used."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        custom_config = ProcessingConfig(
            processing_interval=0.5,
            vacuum_interval=30.0
        )

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=custom_config,
            db_path=self.temp_db.name
        )

        # Verify the configuration is stored
        self.assertEqual(client.processing_config.processing_interval, 0.5)
        self.assertEqual(client.processing_config.vacuum_interval, 30.0)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_publish_failure_handling(self, mock_builder):
        """Test handling of publish failures."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        # Mock failed publish
        mock_future = Mock()
        mock_future.result.side_effect = Exception("Publish failed")
        mock_connection.publish.return_value = (mock_future, 123)

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Simulate connected state
        client.is_connected.set()

        # Publish message
        message_id = client.publish("test/topic", "test payload")

        # Manually trigger message processing
        client._process_pending_messages()

        # Manually trigger the failure callback to simulate async completion
        client._on_publish_complete(mock_future, message_id)

        # Verify message is still in database for retry (not deleted due to failure)
        self.assertEqual(client.get_pending_message_count(), 1)

        # Verify the message is marked as not processing after failure
        conn = sqlite3.connect(self.temp_db.name)
        cursor = conn.cursor()
        cursor.execute("SELECT is_processing FROM message_buffer WHERE id = ?", (message_id,))
        row = cursor.fetchone()
        conn.close()

        # After failure, message should be marked as not processing for retry
        self.assertEqual(row[0], 0)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_database_vacuum_logic(self, mock_builder):
        """Test database vacuum functionality."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Test vacuum worker method exists and can be called
        self.assertTrue(hasattr(client, '_vacuum_worker'))

        # Verify database has vacuum-related pragma settings
        cursor = client.db_connection.cursor()
        cursor.execute("PRAGMA journal_mode")
        journal_mode = cursor.fetchone()[0]
        self.assertEqual(journal_mode, 'wal')

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_start_stop_lifecycle(self, mock_builder):
        """Test client start/stop lifecycle."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Initially not started
        self.assertFalse(client.threads_started)

        # Start client
        client.start()
        self.assertTrue(client.threads_started)

        # Starting again should not cause issues
        client.start()  # Should log warning but not crash
        self.assertTrue(client.threads_started)

        # Stop client
        client.stop()
        self.assertFalse(client.threads_started)

    @patch('pubsub2.robust_mqtt_client.mqtt_connection_builder')
    def test_connection_callbacks(self, mock_builder):
        """Test MQTT connection callbacks."""
        mock_connection = Mock()
        mock_builder.mtls_from_path.return_value = mock_connection

        client = RobustMQTTClient(
            mqtt_config=self.mqtt_config,
            processing_config=self.processing_config,
            db_path=self.temp_db.name
        )

        # Test connection success callback
        client._on_connection_success(mock_connection, None)
        self.assertTrue(client.is_connected.is_set())

        # Test connection failure callback
        client._on_connection_failure(mock_connection, "Test failure")
        self.assertFalse(client.is_connected.is_set())

        # Test connection interrupted callback
        client._on_connection_interrupted(mock_connection, "Test error")
        self.assertFalse(client.is_connected.is_set())

        # Test connection resumed callback
        client._on_connection_resumed(mock_connection, 0, True)
        self.assertTrue(client.is_connected.is_set())


if __name__ == '__main__':
    unittest.main()
