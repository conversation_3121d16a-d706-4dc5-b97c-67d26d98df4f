<?xml version="1.0" encoding="UTF-8"?>
<CycloneDDS xmlns="https://cdds.io/config" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://cdds.io/config https://raw.githubusercontent.com/eclipse-cyclonedds/cyclonedds/master/etc/cyclonedds.xsd">
    <Domain Id="any">
        <General>
            <AllowMulticast>true</AllowMulticast>
            <DontRoute>true</DontRoute>
            <EnableMulticastLoopback>true</EnableMulticastLoopback>
            <RedundantNetworking>true</RedundantNetworking>
            <!-- <Interfaces>
                <NetworkInterface name="uvx0" />
            </Interfaces> -->
        </General>
        <Discovery>
            <EnableTopicDiscoveryEndpoints>true</EnableTopicDiscoveryEndpoints>
            <ParticipantIndex>auto</ParticipantIndex>
            <!-- <Peers>
                <Peer Address="***********"/> 
                <Peer Address="***********"/> 
                <Peer Address="***********"/> 
                <Peer address="host.docker.internal"/>
            </Peers> -->
        </Discovery>
    </Domain>
</CycloneDDS>